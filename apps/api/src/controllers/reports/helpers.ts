import { DateTime } from 'luxon';

export type GeneralObject = {
  id: string;
  date: Date;
  amount: number;
  user_email: string;
  order_date_created: string;
  user_phone: string;
  zoho_contact_id: string;
  first_name: string;
  last_name: string;
  user_company: string;
  user_address_1: string;
  user_address_2: string;
  user_city: string;
  user_state: string;
  user_postcode: string;
  user_country: string;
};

export type SlackMessageBlock = {
  type: string;
  fields: Array<{
    type: string;
    text: string;
  }>;
};

export type PatientResult = {
  email: string;
  orderCount: number;
  orderDates: string[];
  firstRepeatStart: string;
  firstRepeatEnd: string;
  tpStart: string;
  tpEnd: string;
  tpExpired: boolean;
  phone: string;
  usecase: string;
  zohoID: string;
  module: string;
  fullName: string;
  churnedConfidence: 'high' | 'low' | '';
  daysSinceLastOrder: string;
  userCompany: string;
  userAddress_1: string;
  userAddress_2: string;
  userCity: string;
  userState: string;
  userPostcode: string;
  userCountry: string;
};

export interface ZohoRecord {
  zohoId: string;
  module: string;
  useCase: string;
  phoneNumber: string;
  fullName: string;
}

export type CallLog = {
  Date: string;
  Agent: string;
  Phone: string;
  Type: string;
  Status: string;
  Wait: number | string;
  Talk: number | string;
  ACW: number | string;
  Tag: string;
  Sip: string;
  'Call finish initiator': string;
  'Hold counter': string;
  'Total hold time': string;
};

export type AgentReport = {
  agent: string;
  calls: number;
  in: number;
  ans: number;
  percentAns: string;
  out: number;
  suc: number;
  percentSuc: string;
  talkTime: number | string;
  waitTime: number | string;
  bookings: number | string;
};

export type AgentList = {
  softphone: string;
  crm?: string;
};
export const AGENTSLIST: AgentList[] = [
  {
    softphone: 'Ryan Sales',
    crm: 'RYAN',
  },
  {
    softphone: 'Hazel Ops',
    crm: 'HAZEL',
  },
  {
    softphone: 'Ash Ops',
    crm: 'ASH',
  },
  {
    softphone: 'Les Ops',
  },
];
export type ZohoConversion = {
  id: string;
  Booked_on_Call: string;
};

export const getFormatedZohoDate = () => {
  return DateTime.utc()
    .set({ hour: 0, minute: 0, second: 0, millisecond: 0 }) // Set to midnight UTC
    .toFormat("yyyy-MM-dd'T'HH:mm:ssZZ"); // Format with timezone offset
};

export const QUERY_CONVERSION = () => {
  const CMR_CONVERSION = {
    select_query: `
  SELECT
     id,
     Booked_on_Call
     FROM Leads
     WHERE Date_of_Booking_on_Call >= '${getFormatedZohoDate()}' and
     ((Booked_on_Call is not null AND Member_Status = '13 - Consult Booked') and (Full_Name not like '%TESTX%') )
     ORDER BY Date_of_Booking_on_Call`,
  };

  return CMR_CONVERSION;
};

export const findStartAndEndDate = (today: DateTime<true> | DateTime<false>): { start: string; end: string } => {
  const startDate = today.minus({ days: 35 });
  const endDate = startDate.plus({ days: 7 });

  return {
    start: startDate.toFormat('yyyy-MM-dd'),
    end: endDate.toFormat('yyyy-MM-dd'),
  };
};

export const orderFrequency = (tps: string[], orders: GeneralObject[]) => {
  // Create a map to count orders for each email
  const orderCountMap = new Map<string, number>();

  // Initialize counts for all TPS emails to 0
  tps.forEach((email) => orderCountMap.set(email.toLowerCase().trim(), 0));

  // Count orders for each email
  orders.forEach((order) => {
    const email = order.user_email?.toLowerCase().trim();
    if (orderCountMap.has(email)) {
      orderCountMap.set(email, orderCountMap.get(email)! + 1);
    }
  });

  // Convert the map to the desired output format
  const result: { [key: string]: string[] } = {};

  orderCountMap.forEach((count, email) => {
    if (!result[count]) {
      result[count] = [];
    }
    result[count].push(email.toLowerCase().trim());
  });

  return result;
};

export const orderTimeLine = (tps: GeneralObject[], orders: GeneralObject[]) => {
  // Preprocess orders by user email for O(1) lookups
  const ordersMap = new Map<string, Date[]>();
  orders.forEach((order) => {
    const email = order.user_email.toLowerCase().trim();
    const date = new Date(order.order_date_created);
    if (!ordersMap.has(email)) ordersMap.set(email, []);
    ordersMap.get(email)!.push(date);
  });

  // Initialize result structure
  const result = {
    firstRepeat: [] as string[],
    secondRepeat: [] as string[],
    thirdRepeat: [] as string[],
    fourthRepeat: [] as string[],
    fifthRepeat: [] as string[],
    sixthRepeat: [] as string[],
  };

  // Map repeat numbers to result keys
  const repeatKeys = ['first', 'second', 'third', 'fourth', 'fifth', 'sixth'];

  tps.forEach((patient) => {
    const patientEmail = patient.user_email.toLowerCase().trim();
    const tpStart = new Date(patient['Treatment Plans Start Date']);
    const patientOrders = ordersMap.get(patientEmail) || [];
    const repeatsFound = new Set<number>();

    patientOrders.forEach((orderDate) => {
      // Calculate days since TPS start
      const diffMs = orderDate.getTime() - tpStart.getTime();
      if (diffMs < 0) return; // Skip orders before TPS start

      const days = diffMs / (1000 * 60 * 60 * 24);
      const repeatNum = Math.floor(days / 28) + 1;

      // Only track repeats 1-6
      if (repeatNum >= 1 && repeatNum <= 6) {
        repeatsFound.add(repeatNum);
      }
    });

    // Add patient to all relevant repeat arrays
    repeatsFound.forEach((repeat) => {
      const key = `${repeatKeys[repeat - 1]}Repeat` as keyof typeof result;
      result[key].push(patient.user_email.toLowerCase().trim());
    });
  });

  return result;
};

export const noRenewal = (tps: GeneralObject[]) => {
  const today = DateTime.utc();
  const startDate = today.minus({ days: 14 });

  const noRenewedTps = tps
    .filter((f) => {
      const tpEndDate = DateTime.fromISO(f['Treatment Plans End Date'], { zone: 'utc' });
      if (
        tpEndDate.startOf('day') >= startDate.startOf('day') &&
        tpEndDate.startOf('day') <= startDate.plus({ days: 7 }).startOf('day')
      ) {
        return true;
      }

      return false;
    })
    .map((tp) => tp.user_email.toLowerCase().trim());

  return noRenewedTps;
};

export const findChurnedPatients = (tps: GeneralObject[], orders: GeneralObject[]): PatientResult[] => {
  // Preprocess orders by user email
  const ordersMap = new Map<string, Date[]>();
  orders.forEach((order) => {
    const email = order.user_email.toLowerCase().trim();
    const date = new Date(order.order_date_created);
    if (!ordersMap.has(email)) ordersMap.set(email, []);
    ordersMap.get(email)!.push(date);
  });

  // Sort all order dates for each patient
  ordersMap.forEach((dates) => dates.sort((a, b) => a.getTime() - b.getTime()));

  const churnedPatients: PatientResult[] = [];

  tps.forEach((patient) => {
    const email = patient.user_email.toLowerCase().trim();
    const phone = patient.user_phone.trim().replace(/\s+/g, '');
    const patientOrders = ordersMap.get(email);
    if (!patientOrders || patientOrders.length === 0) return;

    const tpStart = new Date(patient['Treatment Plans Start Date']);
    const tpEnd = new Date(patient['Treatment Plans End Date']);
    const firstRepeatStart = new Date(patientOrders[0]);
    const firstRepeatEnd = new Date(firstRepeatStart);
    firstRepeatEnd.setDate(firstRepeatEnd.getDate() + 35); // 28 + 7 days

    // Check if all orders fall within first repeat period
    const allInFirstRepeat = patientOrders.every(
      (orderDate) => orderDate >= firstRepeatStart && orderDate <= firstRepeatEnd,
    );

    // Check if no orders exist after first repeat period
    const hasOrdersAfterFirstRepeat = patientOrders.some((orderDate) => orderDate > firstRepeatEnd);

    const today = DateTime.utc();
    // const startDate = today.minus({ days: 14 });
    const tpEndDate = DateTime.fromISO(patient['Treatment Plans End Date'], { zone: 'utc' });
    let hasTpExpired = false;

    if (tpEndDate.startOf('day') <= today.startOf('day')) {
      hasTpExpired = true;
    }

    const lastOrder = patientOrders[patientOrders.length - 1];
    let churnedConfidence: 'high' | 'low' | '' = '';
    const lastOrderDate = DateTime.fromISO(lastOrder.toISOString().split('T')[0]); // Adjust this based on your date format
    const daysSinceLastOrder = today.diff(lastOrderDate, 'days').days;

    if (daysSinceLastOrder > 28) {
      churnedConfidence = 'high';
    } else {
      churnedConfidence = 'low';
    }

    if (allInFirstRepeat && !hasOrdersAfterFirstRepeat) {
      churnedPatients.push({
        email,
        phone,
        orderCount: patientOrders.length,
        orderDates: patientOrders.map((d) => d.toISOString().split('T')[0]),
        firstRepeatStart: firstRepeatStart.toISOString().split('T')[0],
        firstRepeatEnd: firstRepeatEnd.toISOString().split('T')[0],
        tpStart: tpStart.toISOString().split('T')[0],
        tpEnd: tpEnd.toISOString().split('T')[0],
        tpExpired: hasTpExpired,
        usecase: 'churned',
        zohoID: patient.zoho_contact_id,
        module: 'Contacts',
        fullName: `${patient.first_name} ${patient.last_name}`,
        churnedConfidence,
        daysSinceLastOrder: daysSinceLastOrder.toFixed(0),
        userCompany: patient.user_company,
        userAddress_1: patient.user_address_1,
        userAddress_2: patient.user_address_2,
        userCity: patient.user_city,
        userState: patient.user_state,
        userPostcode: patient.user_postcode,
        userCountry: patient.user_country,
      });
    }
  });

  const sortedChurnedList = churnedPatients.sort((a, b) => {
    const dateA = DateTime.fromISO(a.tpStart);
    const dateB = DateTime.fromISO(b.tpStart);
    return dateA.toMillis() - dateB.toMillis(); // Oldest first
  });
  return sortedChurnedList;
};

export const formatSecondsToHHMMSS = (seconds: number): string => {
  const hrs = Math.floor(seconds / 3600)
    .toString()
    .padStart(2, '0');
  const mins = Math.floor((seconds % 3600) / 60)
    .toString()
    .padStart(2, '0');
  const secs = Math.floor(seconds % 60)
    .toString()
    .padStart(2, '0');
  return `${hrs}:${mins}:${secs}`;
};

export const csvToJson = (csv: string, delimiter: string = ','): CallLog[] => {
  const lines = csv.trim().split('\n');
  const headers = lines[0].split(delimiter).map((h) => h.trim());

  return lines.slice(1).map((line): CallLog => {
    const values = line.split(delimiter).map((v) => v.trim());
    const obj: Partial<CallLog> = {};

    headers.forEach((header, index) => {
      const key = header as keyof CallLog;
      obj[key] = values[index] || '';
    });

    // Ensure required fields exist with empty string defaults
    return {
      Date: obj.Date || '',
      Agent: obj.Agent || '',
      Phone: obj.Phone || '',
      Type: obj.Type === 'Inbound' || obj.Type === 'Outbound' ? obj.Type : 'Inbound', // Default to Inbound if invalid
      Status: obj.Status || '',
      Wait: obj.Wait || '',
      Talk: obj.Talk || '',
      ACW: obj.ACW || '',
      Tag: obj.Tag || '',
      Sip: obj.Sip || '',
      'Call finish initiator': obj['Call finish initiator'] || '',
      'Hold counter': obj['Hold counter'] || '',
      'Total hold time': obj['Total hold time'] || '',
    };
  });
};

export const generateAgentReport = (
  data: CallLog[],
  conversions: ZohoConversion[],
  agentList: AgentList[],
): AgentReport[] => {
  return agentList.map((agent) => {
    const agentName = agent.softphone;
    const crmName = agent.crm || 'N/A';


    // Filter calls for this agent
    const agentCalls = data.filter((call) => call.Agent === agentName);
    const inboundCalls = agentCalls.filter((call) => call.Type === 'Inbound');
    const outboundCalls = agentCalls.filter((call) => call.Type === 'Outbound');
    const answeredInbound = inboundCalls.filter((call) => call.Status === 'Answered');
    const successfulOutbound = outboundCalls.filter((call) => call.Status === 'Answered' && Number(call.Talk) >= 45);

    const totalTalkSeconds = agentCalls.reduce((sum, call) => sum + (Number(call.Talk) || 0), 0);
    const totalWaitSeconds = agentCalls.reduce((sum, call) => sum + (Number(call.Wait) || 0), 0);

    // Count conversions for this agent
    let bookings = 0;
    if (crmName !== 'N/A') {
      const agentConversionPattern = new RegExp(`Yes - Booked on Call ${crmName}`, 'i');
      bookings = conversions.filter((conv) => agentConversionPattern.test(conv.Booked_on_Call)).length;
    }

    const totalCalls = agentCalls.length;
    const inCount = inboundCalls.length;
    const inAns = answeredInbound.length;
    const outCount = outboundCalls.length;
    const outSuc = successfulOutbound.length;

    return {
      agent: agentName,
      calls: totalCalls,
      in: inCount,
      ans: inAns,
      percentAns: inCount === 0 ? '0%' : `${Math.round((inAns / inCount) * 100)}%`,
      out: outCount,
      suc: outSuc,
      percentSuc: outCount === 0 ? '0%' : `${Math.round((outSuc / outCount) * 100)}%`,
      bookings: crmName !== 'N/A' ? bookings : 'N/A',
      talkTime: formatSecondsToHHMMSS(totalTalkSeconds),
      waitTime: formatSecondsToHHMMSS(totalWaitSeconds),
    };
  });
};

type TradName = {
  [key: string]: {
    strength: string;
    name: string;
  };
};

export const tradeNameMapping: TradName = {
  '29-09/18-10/20-11/01-12/15-01': { strength: '29%', name: 'Pink Lemonade' },
  '15-02/19-03/08-04/11-05/30-06': { strength: '29%', name: 'Cinnamon Bomb' },
  '03-10/10-11/15-12/18-01/20-02': { strength: '29%', name: 'Sherb Heights ' },
  '23-03/17-04/14-05/28-06/09-07': { strength: '29%', name: `Karen's Nightmare` },
  '19-02/31-03/16-04/05-05/24-06': { strength: '22%', name: `Cookies 'n' Cuddles` },
  '14-06/26-07/19-08/12-09/21-10': { strength: '29%', name: 'Space Crumble' },
  '26-11/09-12/29-01/22-02/10-03': { strength: '29%', name: 'Toffee Glaze' },
  '08-02/24-03/11-04/22-05/09-06': { strength: '29%', name: 'Hokey Pokey' },
  '07-08/13-09/09-10/30-11/03-12': { strength: '22%', name: 'Bubblegum Nebula' },
  '19-04/03-05/11-06/18-07/26-08': { strength: '29%', name: 'Rainbow Sherbert' },
  '25-10/05-11/11-12/21-01/19-02': { strength: '22%', name: 'Bald Eagle' },
  '21-08/30-09/12-10/24-11/05-12': { strength: '29%', name: 'Supanova Sorbet' },
  '16-03/15-04/16-05/23-06/28-07': { strength: '22%', name: 'Midnight Magic' },
  '04-05/27-06/05-07/28-08/14-09': { strength: '22%', name: 'Raspberry Ripple' },
  '20-01/28-02/05-03/27-04/09-05': { strength: '22%', name: 'Galactic Adventure' },
  '15-07/20-08/03-09/27-10/29-11': { strength: '22%', name: 'Passionfruit Pav' },
  '25-01/12-02/31-03/02-04/28-05': { strength: '29%', name: 'Choc Frappe' },
};

export type Orders = {
  consulting_doctor: string;
  order_number: string;
  order_items: {
    trade_name: string;
    product_name: string;
    quantity: number;
  }[];
};

type SupplyRequest = {
  [key: string]: {
    products: {
      [productName: string]: {
        baseName: string;
        totalQuantity: number;
        totalWeight: number;
        totalBudQuantity: number;
        tradeName: string;
        orderNumber: string;
      };
    };
  };
};
export const generateDoctorSupplyRequests = (orders: Orders[]): SupplyRequest => {
  const doctorSupply = {};

  // Process each doctor's orders
  for (const order of orders) {
    const doctor = order.consulting_doctor;

    // Initialize doctor if not exists
    if (!doctorSupply[doctor]) {
      doctorSupply[doctor] = {
        products: {},
      };
    }

    // Process each product in the order
    for (const item of order.order_items) {
      // Extract base product name (before first hyphen)
      const productKey = item.product_name.split('-')[0].trim();

      // Extract weight (assuming format "XXg")
      const weightMatch = item.product_name.match(/(\d+\.?\d*)g/);
      const weight = weightMatch ? parseFloat(weightMatch[1]) : 0;

      // Format trade dates (remove brackets and replace | with /)
      if (item.trade_name) {
        const formattedTradeDates = item.trade_name?.replace(/[[\]]/g, '').replace(/\|/g, '/');

        // Initialize product if not exists
        if (formattedTradeDates) {
          if (!doctorSupply[doctor].products[productKey]) {
            doctorSupply[doctor].products[productKey] = {
              baseName: productKey,
              totalQuantity: 0,
              totalWeight: 0,
              totalBudQuantity: 0,
              tradeName: formattedTradeDates,
              orderNumber: order.order_number,
            };
          }

          // Update product aggregates
          const product = doctorSupply[doctor].products[productKey];
          product.totalQuantity += item.quantity;
          product.totalWeight += weight * item.quantity;
          product.totalBudQuantity = product.totalWeight / 3.5;
        }
      }
    }
  }

  return doctorSupply;
};

interface ProductRequest {
  drName: string;
  budQuantity: number;
}

interface ProductSummary {
  requests: ProductRequest[];
  totalBudQuantity: number;
  tradeName: string;
  strength: string;
  balance: number;
  orderNumber: string;
}

interface ProductCentricSupply {
  [productName: string]: ProductSummary;
}

export type CurrentBalance = {
  id: string;
  name: string;
  code: string;
  balance: number;
};

export const generateProductCentricSupply = (
  doctorSupply: ReturnType<typeof generateDoctorSupplyRequests>,
  balance: CurrentBalance[],
  newOrders: Orders[],
): ProductCentricSupply => {
  const productCentricSupply: ProductCentricSupply = {};

  // First pass: Collect all product data from doctors
  for (const [drName, doctorData] of Object.entries(doctorSupply)) {
    for (const [productName, productData] of Object.entries(doctorData.products)) {
      if (!productCentricSupply[productName]) {
        // Initialize with empty requests and placeholder balance
        productCentricSupply[productName] = {
          requests: [],
          totalBudQuantity: 0,
          tradeName: productData.tradeName,
          strength: tradeNameMapping[productData.tradeName]?.strength || 'Unknown',
          balance: balance.find((b) => b.code === productData.tradeName)?.balance || 0, // Starting balance (would normally come from inventory)
          orderNumber: productData.orderNumber,
        };
      }

      // Add this doctor's request
      productCentricSupply[productName].requests.push({
        drName,
        budQuantity: productData.totalBudQuantity,
      });

      // Update total
      productCentricSupply[productName].totalBudQuantity += productData.totalBudQuantity;
    }
  }

  // Second pass: Calculate remaining balance
  for (const productName in productCentricSupply) {
    const isNewOrder = newOrders.find((n) => n.order_number === productCentricSupply[productName].orderNumber);
    if (isNewOrder) {
      productCentricSupply[productName].balance -= productCentricSupply[productName].totalBudQuantity;
    }
  }

  return productCentricSupply;
};
